# Super User Accounts for System Development

This document contains the login credentials for system developer accounts created for testing and development purposes.

## Account Details

### Admin Account
- **Email**: <EMAIL>
- **Member ID**: sdadmin
- **Password**: Map135
- **Role**: Administrator
- **Access**: Full system access, all permissions

### Manager Account
- **Email**: <EMAIL>
- **Member ID**: sdmanager
- **Password**: Map135
- **Role**: Branch Manager
- **Access**: Branch management, loan approvals, member oversight

### Field Officer Account
- **Email**: <EMAIL>
- **Member ID**: sdofficer
- **Password**: Map135
- **Role**: Field Officer
- **Access**: Member management, loan processing, data entry

### Member Account
- **Email**: <EMAIL>
- **Member ID**: sdmember
- **Password**: Map135
- **Role**: Member
- **Access**: Personal financial data, loan status, savings information

## Login Instructions

1. Navigate to the login page: `http://www.sonalibd.org/login`
2. Use the email address as the username
3. Enter the password: `Map135`
4. You will be automatically redirected to the appropriate dashboard based on your role

## Security Notes

- These accounts are for development and testing purposes only
- Change passwords in production environment
- Accounts have `is_active` status set to `true`
- All accounts are verified (`email_verified_at` is set)

## Role-Based Dashboard Access

After login, users are redirected to:
- **Admin**: `/admin/dashboard`
- **Manager**: `/manager/dashboard`
- **Field Officer**: `/field-officer/dashboard`
- **Member**: `/member/dashboard`

## Database Information

These accounts are created through the `AdminUserSeeder` class and have proper role assignments through the Spatie Laravel Permission package.

---
*Created: $(date)*
*System: Sonali Microfinance Management System*
