@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Modern CSS Variables for Enhanced Styling */
:root {
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

    --gradient-primary: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    --gradient-accent: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* Glassmorphism Effects */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modern Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: none;
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

/* Modern Button Styles - Commented out for now due to custom color issues */
/*
.btn-primary {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white shadow-lg hover:shadow-xl focus:ring-primary-500;
}
*/

/* Modern Form Styles - Simplified */
.form-group {
    margin-bottom: 1.5rem;
}

.form-input {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.75rem;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
    background-color: white;
    transform: scale(1.02);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

/* Modern Card Styles - Simplified */
.card-modern {
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(229, 231, 235, 0.5);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card-modern:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    background: linear-gradient(90deg, rgba(249, 250, 251, 0.8) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.card-body {
    padding: 1.25rem 1.5rem;
}

/* Simplified Modern Styles */
.dashboard-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(229, 231, 235, 0.5);
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
    background: white;
}

.dashboard-card-gradient {
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    color: white;
    transition: all 0.3s ease;
}

.dashboard-card-gradient:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.nav-modern {
    background: linear-gradient(180deg, #ffffff 0%, rgba(249, 250, 251, 0.5) 100%);
    backdrop-filter: blur(4px);
    border-right: 1px solid rgba(229, 231, 235, 0.5);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.nav-link-modern {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    margin: 0 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.75rem;
    color: #6b7280;
    transition: all 0.3s ease;
}

.nav-link-modern:hover {
    color: #111827;
    background: linear-gradient(90deg, #f0fdf4 0%, #dcfce7 100%);
}

.nav-link-modern.active {
    background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Modern Alert Styles */
.alert {
    padding: 1rem;
    border-radius: 0.75rem;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.alert:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: linear-gradient(90deg, #f0fdf4 0%, #dcfce7 100%);
    border-color: #22c55e;
    color: #166534;
}

.alert-danger {
    background: linear-gradient(90deg, #fef2f2 0%, #fee2e2 100%);
    border-color: #ef4444;
    color: #991b1b;
}

/* Responsive Navigation */
@media (max-width: 768px) {
    .mobile-menu-open {
        display: block;
    }

    .mobile-menu-closed {
        display: none;
    }
}
