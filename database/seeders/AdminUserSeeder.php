<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create system developer admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Developer Admin',
                'password' => Hash::make('Map135'),
                'role' => 'admin',
                'member_id' => 'sdadmin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        if (!$admin->hasRole('admin')) {
            $admin->assignRole('admin');
        }

        // Create system developer manager user
        $manager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Developer Manager',
                'password' => Hash::make('Map135'),
                'role' => 'manager',
                'member_id' => 'sdmanager',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        if (!$manager->hasRole('manager')) {
            $manager->assignRole('manager');
        }

        // Create system developer field officer user
        $fieldOfficer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Developer Field Officer',
                'password' => Hash::make('Map135'),
                'role' => 'field_officer',
                'member_id' => 'sdofficer',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        if (!$fieldOfficer->hasRole('field-officer')) {
            $fieldOfficer->assignRole('field-officer');
        }

        // Create system developer member user
        $member = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Developer Member',
                'password' => Hash::make('Map135'),
                'role' => 'member',
                'member_id' => 'sdmember',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        if (!$member->hasRole('member')) {
            $member->assignRole('member');
        }

        // Keep original admin user for backward compatibility
        $originalAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password123'),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        if (!$originalAdmin->hasRole('admin')) {
            $originalAdmin->assignRole('admin');
        }
    }
}
