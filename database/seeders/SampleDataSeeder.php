<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Savings;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample branches
        $dhakaBranch = Branch::create([
            'name' => 'Dhaka Main Branch',
            'address' => 'House 123, Road 15, Dhanmondi, Dhaka-1205',
            'manager_id' => User::where('email', '<EMAIL>')->first()->id,
        ]);

        $chittagongBranch = Branch::create([
            'name' => 'Chittagong Branch',
            'address' => 'GEC Circle, Chittagong-4000',
        ]);

        $sylhetBranch = Branch::create([
            'name' => 'Sylhet Branch',
            'address' => 'Zindabazar, Sylhet-3100',
        ]);

        // Get field officer
        $fieldOfficer = User::where('email', '<EMAIL>')->first();

        // Create sample members
        $members = [
            [
                'member_id' => 'MEM001',
                'name' => 'Fatima Begum',
                'father_or_husband_name' => '<PERSON> <PERSON>',
                'mother_name' => 'Rashida Begum',
                'date_of_birth' => '1985-03-15',
                'nid_number' => '1234567890123',
                'phone_number' => '***********',
                'present_address' => 'House 45, Road 7, Mohammadpur, Dhaka',
                'permanent_address' => 'Village: Rampur, Upazila: Savar, District: Dhaka',
                'occupation' => 'Small Business Owner',
                'branch_id' => $dhakaBranch->id,
                'created_by' => $fieldOfficer->id,
            ],
            [
                'member_id' => 'MEM002',
                'name' => 'Mohammad Karim',
                'father_or_husband_name' => 'Abdul Karim',
                'mother_name' => 'Salma Begum',
                'date_of_birth' => '1978-08-22',
                'nid_number' => '2345678901234',
                'phone_number' => '***********',
                'present_address' => 'House 78, Road 12, Uttara, Dhaka',
                'permanent_address' => 'Village: Goalanda, Upazila: Rajbari, District: Rajbari',
                'occupation' => 'Farmer',
                'branch_id' => $dhakaBranch->id,
                'created_by' => $fieldOfficer->id,
            ],
            [
                'member_id' => 'MEM003',
                'name' => 'Rashida Khatun',
                'father_or_husband_name' => 'Abdur Rahman',
                'mother_name' => 'Amina Begum',
                'date_of_birth' => '1990-12-10',
                'nid_number' => '3456789012345',
                'phone_number' => '01912345680',
                'present_address' => 'Agrabad, Chittagong',
                'permanent_address' => 'Village: Hathazari, Upazila: Hathazari, District: Chittagong',
                'occupation' => 'Tailor',
                'branch_id' => $chittagongBranch->id,
                'created_by' => $fieldOfficer->id,
            ],
        ];

        foreach ($members as $memberData) {
            Member::create($memberData);
        }
    }
}
